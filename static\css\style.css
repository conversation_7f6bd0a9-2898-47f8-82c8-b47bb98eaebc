/* Genel Reset & Font */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #f8fafc;
  color: #1f2937;
  margin: 0;
  padding: 0;
  line-height: 1.6;
  opacity: 0;
  transition: opacity 0.8s ease;
}

body.loaded {
  opacity: 1;
}

/* Responsive Grid Fix */
@media (max-width: 768px) {
  .about-intro {
    display: block !important;
    gap: 30px !important;
  }

  .about-intro > div {
    margin-bottom: 30px;
  }
}

/* Container */
.container {
  max-width: 1140px;
  margin: 0 auto;
  padding: 20px;
}

/* Başlıklar */
h1, h2, h3 {
  font-weight: 700;
  color: #111827;
  margin-bottom: 15px;
}

/* Linkler */
a {
  color: #2563eb;
  text-decoration: none;
  transition: color 0.3s ease;
}
a:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

/* Butonlar */
.btn-primary {
  background-color: #2563eb;
  color: white;
  padding: 12px 28px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}
.btn-primary:hover {
  background-color: #1e40af;
  box-shadow: 0 6px 12px rgba(30, 64, 175, 0.5);
}

.hero-section {
  padding: 120px 0;
  background: linear-gradient(135deg, #48cae4, #ade8f4);
  text-align: center;
}

/* MODERN NAVBAR */
nav.navbar {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0,0,0,0.1);
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  position: fixed;
  top: 0;
  z-index: 1050;
  width: 100%;
  padding: 0.75rem 0;
  transition: all 0.3s ease;
}

/* Navbar Brand */
.navbar-brand {
  font-size: 1.5rem;
  font-weight: 800;
  color: #1e40af !important;
  text-decoration: none;
  transition: all 0.3s ease;
}

.navbar-brand:hover {
  transform: translateY(-1px);
}

.brand-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.navbar-brand:hover .brand-icon {
  transform: rotate(5deg) scale(1.05);
}

/* Modern Hamburger Menu */
.hamburger-menu {
  width: 24px;
  height: 18px;
  position: relative;
  cursor: pointer;
}

.hamburger-menu span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: #374151;
  border-radius: 2px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: 0.25s ease-in-out;
}

.hamburger-menu span:nth-child(1) {
  top: 0px;
}

.hamburger-menu span:nth-child(2) {
  top: 8px;
}

.hamburger-menu span:nth-child(3) {
  top: 16px;
}

.navbar-toggler[aria-expanded="true"] .hamburger-menu span:nth-child(1) {
  top: 8px;
  transform: rotate(135deg);
}

.navbar-toggler[aria-expanded="true"] .hamburger-menu span:nth-child(2) {
  opacity: 0;
  left: -60px;
}

.navbar-toggler[aria-expanded="true"] .hamburger-menu span:nth-child(3) {
  top: 8px;
  transform: rotate(-135deg);
}

/* Navigation Links */
.navbar .nav-link {
  color: #374151 !important;
  padding: 12px 16px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  margin: 0 2px;
}

.navbar .nav-link:hover {
  color: #3b82f6 !important;
  background: rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.navbar .nav-link.active {
  color: #3b82f6 !important;
  background: rgba(59, 130, 246, 0.15);
}

/* Dropdown Menu */
.dropdown-menu {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px);
  border: none !important;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0,0,0,0.15);
  padding: 8px;
  margin-top: 8px;
  min-width: 220px;
}

.dropdown-item {
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 600;
  color: #374151;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.dropdown-item:hover {
  background: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
  transform: translateX(4px);
}

.dropdown-item i {
  width: 20px;
  text-align: center;
}

/* CTA Button in Navbar */
.navbar .btn-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
  border: none;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  font-weight: 700;
  transition: all 0.3s ease;
}

.navbar .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* Navbar Scroll Effect */
.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98) !important;
  box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

/* Body Padding for Fixed Navbar */
body {
  padding-top: 80px;
}

/* PREMIUM FOOTER */
.footer-section {
  margin-top: auto;
  position: relative;
}

/* Premium Animations */
@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.1; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

/* CTA Section */
.cta-icon-wrapper:hover .cta-icon {
  transform: scale(1.05);
  box-shadow: 0 25px 80px rgba(251, 191, 36, 0.6);
}

.footer-cta .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 20px 50px rgba(251, 191, 36, 0.5) !important;
}

.footer-cta .btn:hover .position-absolute {
  transform: translateX(100%);
}

.footer-cta .btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #fbbf24;
  color: #fbbf24 !important;
  box-shadow: 0 10px 30px rgba(251, 191, 36, 0.2);
}

/* Feature Cards */
.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0,0,0,0.2) !important;
}

/* Footer Links */
.footer-link {
  position: relative;
  transition: all 0.3s ease;
}

.footer-link:hover {
  color: #fbbf24 !important;
  transform: translateX(8px);
}

.footer-link::before {
  content: '';
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  transition: width 0.3s ease;
}

.footer-link:hover::before {
  width: 10px;
}

/* Premium Social Links */
.social-link {
  position: relative;
  transition: all 0.3s ease;
}

.social-link:hover {
  transform: translateY(-5px) scale(1.1);
  box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  border-radius: 12px;
}

.social-link:hover::before {
  transform: translateX(100%);
}

/* Premium Contact Items */
.contact-item {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.contact-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  transition: left 0.6s ease;
}

.contact-item:hover::before {
  left: 100%;
}

.contact-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Contact Icons */
.contact-icon {
  transition: all 0.3s ease;
  position: relative;
}

.contact-item:hover .contact-icon {
  transform: scale(1.05) rotate(5deg);
}

/* Phone Links */
.phone-link, .email-link {
  transition: all 0.3s ease;
  position: relative;
}

.phone-link:hover, .email-link:hover {
  color: #fbbf24 !important;
  transform: translateX(5px);
}

.phone-link:hover i, .email-link:hover i {
  color: #fbbf24 !important;
}

/* Working Hours */
.working-hours {
  font-size: 0.85rem;
}

.emergency-service {
  transition: all 0.3s ease;
}

.emergency-service:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

/* Stats Mini */
.stat-mini {
  transition: all 0.3s ease;
}

.stat-mini:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Brand Icon */
.brand-icon {
  transition: all 0.3s ease;
}

.footer-brand:hover .brand-icon {
  transform: rotate(5deg) scale(1.05);
}

/* Footer Responsive */
@media (max-width: 768px) {
  .footer-cta .d-flex.flex-column.flex-sm-row {
    flex-direction: column !important;
  }

  .footer-cta .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .footer-main .row.g-5 {
    gap: 2rem !important;
  }

  .cta-icon {
    width: 80px !important;
    height: 80px !important;
  }

  .cta-icon i {
    font-size: 2rem !important;
  }

  .social-link {
    width: 40px !important;
    height: 40px !important;
  }
}

h1, h2, h3 {
  font-weight: 700;
  color: #003566;
}

.hero-section h1 {
  font-size: 3rem;
  font-weight: 700;
  animation: fadeInDown 1s ease;
}

.hero-section p {
  font-size: 1.2rem;
  animation: fadeInUp 1.2s ease;
}

@keyframes fadeInDown {
  0% { opacity: 0; transform: translateY(-50px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
  0% { opacity: 0; transform: translateY(50px); }
  100% { opacity: 1; transform: translateY(0); }
}

.lead {
  color: #000000;
  font-size: 1.2rem;
}

.btn-primary {
  background-color: #0077b6;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: #023e8a;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(255, 0, 0, 0.87);
}

.img-fluid {
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
  max-width: 100%;
  height: auto;
  display: block;
  border-radius: 10px;
  transition: transform 0.5s;
}

.img-fluid:hover {
  transform: scale(1.05);
}

/* Mobil için özel stiller */
@media (max-width: 768px) {
  .hero-section {
    padding: 80px 20px 60px 20px;
    text-align: center;
  }

  .hero-section h1 {
    font-size: 2rem;
  }

  .hero-section p {
    font-size: 1rem;
  }

  /* Mobile Navbar */
  .navbar-nav .nav-link {
    margin: 4px 0;
    padding: 12px 16px;
    border-radius: 8px;
  }

  .navbar-collapse {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    margin-top: 12px;
    padding: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  }

  .dropdown-menu {
    position: static !important;
    transform: none !important;
    background: rgba(59, 130, 246, 0.05) !important;
    border-radius: 8px;
    margin: 8px 0;
    box-shadow: none;
  }

  .navbar .btn-primary {
    margin-top: 12px;
    width: 100%;
    justify-content: center;
  }

  .img-fluid {
    margin-top: 20px;
  }
}

/* Global overflow fix */
html, body {
  max-width: 100%;
  overflow-x: hidden;  /* yatay scroll'u kapat */
}

/* Bootstrap row/col taşma kontrolü */
.row, .col {
  margin: 0 !important;
  padding: 0 !important;
}

/* Footer */
footer {
  background-color: #1e293b;
  color: #cbd5e1;
  padding: 30px 0;
  text-align: center;
  font-size: 0.9rem;
  margin-top: 50px;
}

.footer-link {
  color: #ddd;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-link:hover {
  color: #fff;
}

.social-icons a {
  color: #ddd;
  font-size: 1.3rem;
  transition: color 0.3s, transform 0.3s;
}

.social-icons a:hover {
  color: #fff;
  transform: scale(1.2);
}

footer h5 {
  font-weight: bold;
  margin-bottom: 1rem;
}

footer p, footer li {
  font-size: 0.95rem;
}

hr.bg-light {
  opacity: 0.2;
}

/* Hakkımızda sayfası stilleri */
.about-banner {
  height: 300px;
  background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)),
              url('/static/images/banner-about.jpg') center/cover no-repeat;
  background-size: cover;
  background-position: center center;
  background-attachment: scroll;
}

.about-banner h1 {
  font-size: 3rem;
  font-weight: 700;
}

.about-content h2 {
  color: #0077b6;
}

.about-content p {
  font-size: 1.1rem;
  color: #ffffff;
}

.about-content img {
  transition: transform 0.5s, box-shadow 0.5s;
}

.about-content img:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(206, 122, 122, 0.2);
}

.vision-card {
  background: linear-gradient(135deg, #48cae4, #00b4d8);
  color: rgb(255, 255, 255);
  border: none;
}

.mission-card {
  background: linear-gradient(135deg, #0077b6, #023e8a);
  color: rgb(255, 255, 255);
  border: none;
}

.card h4 {
  font-weight: bold;
}

.card p {
  font-size: 1rem;
}

/* Şirket tanıtım bölümü özel rengi */
.company-intro p {
  color: #2c3e50 !important;
  font-weight: 500;
}

/* İletişim sayfası stilleri */
.contact-banner {
  height: 300px;
  background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)),
              url('/static/images/banner-contact.jpg') center/cover no-repeat;
  background-size: cover;
  background-position: center center;
  background-attachment: scroll;
}

.contact-banner h1 {
  font-size: 3rem;
  font-weight: 700;
}

.contact-section h2 {
  color: #0077b6;
}

.contact-section .form-control {
  border-radius: 8px;
  border: 1px solid #ccc;
}

.contact-section .btn-primary {
  background-color: #0077b6;
  border: none;
}

.contact-section .btn-primary:hover {
  background-color: #023e8a;
}

.contact-section .social-icons a {
  color: #0077b6;
  font-size: 1.4rem;
  transition: color 0.3s, transform 0.3s;
}

.contact-section .social-icons a:hover {
  color: #023e8a;
  transform: scale(1.2);
}

/* Hizmetler sayfası stilleri */
.services-banner {
  height: 300px;
  background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)),
              url('/static/images/banner-services.jpg') center/cover no-repeat;
  background-size: cover;
  background-position: center center;
  background-attachment: scroll;
}

.services-banner h1 {
  font-size: 3rem;
  font-weight: 700;
}

.service-card {
  transition: transform 0.4s, box-shadow 0.4s;
  border: none;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

.service-icon {
  font-size: 2rem;
  color: #0077b6;
}

/* Hizmet detay sayfaları */
.service-detail-banner {
  height: 250px;
  font-size: 2.5rem;
  font-weight: 700;
}

.service-detail-banner h1 {
  font-size: 2.5rem;
  font-weight: 700;
}

/* Klima banner */
.klima-banner {
  background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)),
              url('/static/images/banner-klima.jpg') center/contain no-repeat;
  background-size: cover;
  background-position: center center;
}

/* Kombi banner */
.kombi-banner {
  background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)),
              url('/static/images/banner-kombi.jpg') center/contain no-repeat;
  background-size: cover;
  background-position: center center;
}

/* İnşaat banner */
.insaat-banner {
  background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)),
              url('/static/images/banner-insaat.jpg') center/contain no-repeat;
  background-size: cover;
  background-position: center center;
}

/* Boya & Badana banner */
.boya-banner {
  background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)),
              url('/static/images/banner-boya.jpg') center/contain no-repeat;
  background-size: cover;
  background-position: center center;
}

/* SSS sayfası stilleri */
.sss-banner {
  height: 300px;
  background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)),
              url('/static/images/banner-sss.jpg') center/cover no-repeat;
  background-size: cover;
  background-position: center center;
  background-attachment: scroll;
}

.sss-banner h1 {
  font-size: 3rem;
  font-weight: 700;
}

.accordion-button:not(.collapsed) {
  background-color: #0077b6;
  color: white;
}

.accordion-button:hover {
  background-color: #0096c7;
  color: white;
}

.accordion-item {
  border: 1px solid #dee2e6;
  margin-bottom: 10px;
  border-radius: 8px;
}

.accordion-button {
  font-weight: 500;
  padding: 1rem 1.25rem;
}

.accordion-body {
  padding: 1.25rem;
  background-color: #f8f9fa;
}

/* Genel Hizmet Sayfası Stili */
.hizmet-banner {
  height: 240px;
  background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)),
              url('/static/images/hizmet-banner.jpg') center/cover no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-shadow: 1px 1px 3px black;
}

.hizmet-banner h1 {
  font-size: 2.5rem;
  animation: fadeInDown 1s ease-in-out;
}

.hizmet-content {
  padding: 50px 20px;
  animation: fadeIn 1.5s ease-in-out;
}

.hizmet-content h2 {
  color: #1e3a8a;
  font-size: 2rem;
  margin-bottom: 20px;
}

.hizmet-content p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #333;
}

.hizmet-content img {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
  max-width: 100%;
}

.hizmet-content img:hover {
  transform: scale(1.02);
}

/* Animasyonlar */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  } to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  } to {
    opacity: 1;
  }
}

/* Dropdown menu styling */
.dropdown-menu {
  border: none;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  border-radius: 8px;
}

.dropdown-item {
  padding: 0.75rem 1.25rem;
  transition: background-color 0.3s;
}

.dropdown-item:hover {
  background-color: #0077b6;
  color: white;
}

.dropdown-divider {
  margin: 0.5rem 0;
}

/* Mobil Uyum */
@media (max-width: 768px) {
  .hizmet-banner h1 {
    font-size: 1.8rem;
    text-align: center;
    padding: 0 10px;
  }

  .hizmet-content {
    padding: 30px 10px;
  }

  .hizmet-content h2 {
    font-size: 1.5rem;
  }

  .hizmet-content p {
    font-size: 1rem;
  }
}

/* Animasyon */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(25px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Genel fadeIn animasyonunu kolayca eklemek için */
.fade-in-up {
  animation: fadeInUp 0.8s ease forwards;
}

/* Service card hover effects */
.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

/* Floating animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Hero section enhancements */
.hero-section {
  position: relative;
  overflow: hidden;
}

/* Testimonial cards hover effect */
.testimonials-section > div > div > div:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.12) !important;
  transition: all 0.3s ease;
}

/* CTA section animation */
@keyframes backgroundMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(-50px, -50px); }
}

/* Custom CSS Icons */
.icon-lightning {
  width: 20px;
  height: 20px;
  display: inline-block;
  position: relative;
  color: #fbbf24;
}

.icon-lightning::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 12px solid currentColor;
  top: 0;
  left: 4px;
}

.icon-lightning::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 8px solid currentColor;
  bottom: 0;
  right: 4px;
}

.icon-check {
  width: 20px;
  height: 20px;
  display: inline-block;
  position: relative;
  color: #10b981;
}

.icon-check::before {
  content: '';
  position: absolute;
  width: 6px;
  height: 12px;
  border: solid currentColor;
  border-width: 0 3px 3px 0;
  transform: rotate(45deg);
  top: 2px;
  left: 6px;
}

.icon-user {
  width: 20px;
  height: 20px;
  display: inline-block;
  position: relative;
  color: #3b82f6;
}

.icon-user::before {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  top: 2px;
  left: 6px;
}

.icon-user::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 8px;
  border-radius: 8px 8px 0 0;
  background: currentColor;
  bottom: 2px;
  left: 2px;
}

.icon-phone {
  width: 20px;
  height: 20px;
  display: inline-block;
  position: relative;
  color: #111827;
}

.icon-phone::before {
  content: '';
  position: absolute;
  width: 12px;
  height: 16px;
  border: 2px solid currentColor;
  border-radius: 3px;
  top: 2px;
  left: 4px;
}

.icon-phone::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 2px;
  background: currentColor;
  border-radius: 1px;
  bottom: 4px;
  left: 7px;
}

.icon-shield {
  width: 20px;
  height: 20px;
  display: inline-block;
  position: relative;
  color: #3b82f6;
}

.icon-shield::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 16px solid currentColor;
  top: 2px;
  left: 2px;
}

.icon-certificate {
  width: 20px;
  height: 20px;
  display: inline-block;
  position: relative;
  color: #f59e0b;
}

.icon-certificate::before {
  content: '';
  position: absolute;
  width: 14px;
  height: 12px;
  border: 2px solid currentColor;
  border-radius: 2px;
  top: 2px;
  left: 3px;
}

.icon-certificate::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-top: 4px solid currentColor;
  bottom: 2px;
  left: 7px;
}

.icon-star {
  width: 20px;
  height: 20px;
  display: inline-block;
  position: relative;
  color: #fbbf24;
}

.icon-star::before {
  content: '★';
  position: absolute;
  font-size: 18px;
  color: currentColor;
  top: 1px;
  left: 1px;
}

.icon-star-filled {
  width: 20px;
  height: 20px;
  display: inline-block;
  position: relative;
  color: #fbbf24;
}

.icon-star-filled::before {
  content: '★';
  position: absolute;
  font-size: 18px;
  color: currentColor;
  top: 1px;
  left: 1px;
}

.icon-snowflake {
  width: 24px;
  height: 24px;
  display: inline-block;
  position: relative;
  color: white;
}

.icon-snowflake::before {
  content: '';
  position: absolute;
  width: 2px;
  height: 20px;
  background: currentColor;
  top: 2px;
  left: 11px;
}

.icon-snowflake::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 2px;
  background: currentColor;
  top: 11px;
  left: 2px;
}

.icon-fire {
  width: 24px;
  height: 24px;
  display: inline-block;
  position: relative;
  color: white;
}

.icon-fire::before {
  content: '';
  position: absolute;
  width: 12px;
  height: 16px;
  background: currentColor;
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  top: 4px;
  left: 6px;
}

.icon-building {
  width: 24px;
  height: 24px;
  display: inline-block;
  position: relative;
  color: white;
}

.icon-building::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 18px;
  border: 2px solid currentColor;
  border-radius: 2px;
  top: 2px;
  left: 4px;
}

.icon-building::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  border: 1px solid currentColor;
  top: 6px;
  left: 9px;
}

.icon-paint {
  width: 24px;
  height: 24px;
  display: inline-block;
  position: relative;
  color: white;
}

.icon-paint::before {
  content: '';
  position: absolute;
  width: 8px;
  height: 12px;
  background: currentColor;
  border-radius: 0 0 4px 4px;
  top: 8px;
  left: 8px;
}

.icon-paint::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 6px;
  background: currentColor;
  border-radius: 6px 6px 0 0;
  top: 2px;
  left: 6px;
}

.icon-message {
  width: 20px;
  height: 20px;
  display: inline-block;
  position: relative;
  color: white;
}

.icon-message::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 12px;
  border: 2px solid currentColor;
  border-radius: 3px;
  top: 2px;
  left: 2px;
}

.icon-message::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid currentColor;
  bottom: 4px;
  left: 6px;
}

.icon-gift {
  width: 20px;
  height: 20px;
  display: inline-block;
  position: relative;
  color: #fbbf24;
}

.icon-gift::before {
  content: '';
  position: absolute;
  width: 12px;
  height: 10px;
  border: 2px solid currentColor;
  border-radius: 2px;
  bottom: 2px;
  left: 4px;
}

.icon-gift::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 2px;
  background: currentColor;
  top: 8px;
  left: 2px;
}

/* CTA section responsive */
@media (max-width: 768px) {
  .cta-section div[style*="display: flex"] {
    flex-direction: column !important;
    align-items: center !important;
  }

  .hero-section h1 {
    font-size: 2.2rem !important;
  }

  .hero-section h1 span:last-child {
    font-size: 1.6rem !important;
  }

  .hero-section p {
    font-size: 1.1rem !important;
  }

  .hero-section > div > div > div {
    padding: 30px 20px !important;
  }

  .stats-section h2 {
    font-size: 2.2rem !important;
  }

  .stats-section > div > div:first-child > div {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
    gap: 20px !important;
  }

  .cta-section h2 {
    font-size: 2.2rem !important;
  }

  .testimonials-section > div > div:last-child {
    grid-template-columns: 1fr !important;
  }
}

/* Modern Animasyonlar */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Hover Efektleri */
.service-card:hover {
  transform: translateY(-10px) !important;
  box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4) !important;
}

/* Fade In Animasyonları */
.fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

.fade-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.fade-in-right {
  animation: slideInRight 0.8s ease-out;
}

/* Loading Animasyonu */
.loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.5s ease;
}

.loading.hidden {
  opacity: 0;
  pointer-events: none;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Smooth Scroll */
html {
  scroll-behavior: smooth;
}

/* Service Card Hover Effects */
.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.service-card:hover .service-header > div:last-child {
  transform: translateX(0) !important;
}

/* Feature Card Hover Effects */
.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.12) !important;
}

/* Testimonial Card Hover Effects */
.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.12) !important;
}

/* Stat Card Hover Effects */
.stat-card:hover {
  transform: scale(1.05);
  background: rgba(255,255,255,0.1) !important;
}

/* Hero Section Responsive */
@media (max-width: 991.98px) {
  .hero-section .row {
    text-align: center;
  }

  .hero-section .floating-card {
    display: none !important;
  }

  .hero-title {
    font-size: 3rem !important;
  }

  .hero-description {
    font-size: 1.1rem !important;
  }
}

@media (max-width: 767.98px) {
  .hero-section {
    min-height: 80vh !important;
    padding-top: 100px !important;
  }

  .hero-title {
    font-size: 2.5rem !important;
  }

  .hero-description {
    font-size: 1rem !important;
  }

  .feature-card {
    padding: 15px !important;
  }

  .feature-card .small {
    font-size: 0.8rem !important;
  }
}

/* Mobile Optimizations */
@media (max-width: 575.98px) {
  .hero-section {
    padding-top: 120px !important;
  }

  .hero-title {
    font-size: 2rem !important;
  }

  .badge-text {
    font-size: 0.7rem !important;
  }

  .display-4 {
    font-size: 2rem !important;
  }

  .display-5 {
    font-size: 1.8rem !important;
  }

  .lead {
    font-size: 1rem !important;
  }

  .btn-lg {
    padding: 12px 24px !important;
    font-size: 1rem !important;
  }

  .service-card .p-4 {
    padding: 20px !important;
  }

  .feature-card .p-4 {
    padding: 20px !important;
  }

  .testimonial-card .p-4 {
    padding: 20px !important;
  }

  .stats-section .p-4 {
    padding: 20px !important;
  }

  .trust-section .p-4 {
    padding: 20px !important;
  }

  .cta-section .py-5 {
    padding: 40px 0 !important;
  }
}

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 768px) {
  .hero-section .row {
    gap: 40px;
  }

  .floating-card {
    display: none !important;
  }
}

/* General Responsive Fixes */
@media (max-width: 768px) {
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }

  /* Small Mobile Navbar */
  .navbar .nav-link {
    padding: 10px 12px;
    font-size: 0.9rem;
  }

  .brand-icon {
    width: 35px;
    height: 35px;
  }

  .navbar-brand {
    font-size: 1.3rem;
  }

  .btn-primary {
    padding: 10px 24px;
    font-size: 0.9rem;
  }

  /* Section Spacing */
  section.py-5 {
    padding: 40px 0 !important;
  }

  .mb-5 {
    margin-bottom: 2rem !important;
  }

  .mb-4 {
    margin-bottom: 1.5rem !important;
  }

  /* Card Spacing */
  .g-4 > * {
    margin-bottom: 1.5rem;
  }

  /* Text Adjustments */
  .fs-1 {
    font-size: 2rem !important;
  }

  .fs-4 {
    font-size: 1.2rem !important;
  }

  /* Button Adjustments */
  .d-flex.gap-3 {
    gap: 1rem !important;
  }

  .rounded-pill {
    padding: 10px 20px !important;
  }
}

/* Print Styles */
@media print {
  .navbar, .btn-primary, footer {
    display: none !important;
  }

  body {
    padding-top: 0 !important;
    color: #000 !important;
    background: #fff !important;
  }
}

/* ===== PREMIUM HERO ANIMATIONS ===== */

/* Enhanced Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(1deg);
  }
  66% {
    transform: translateY(-25px) rotate(-1deg);
  }
}

/* Shimmer Effect for Premium Elements */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Underline Growth Animation */
@keyframes underlineGrow {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}

/* Pulse Glow Effect */
@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(251, 191, 36, 0.6);
  }
}

/* Gradient Animation */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Text Glow Animation */
@keyframes textGlow {
  0%, 100% {
    text-shadow: 0 0 10px rgba(251, 191, 36, 0.3);
  }
  50% {
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.6), 0 0 30px rgba(251, 191, 36, 0.4);
  }
}

/* ===== HERO INTERACTIVE EFFECTS ===== */

/* Feature Card Hover Effects */
.feature-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 40px rgba(0,0,0,0.2) !important;
}

.feature-card:hover .feature-shine {
  transform: translateX(100%);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}

/* CTA Button Hover Effects */
.btn:hover .position-absolute {
  transform: translateX(100%) !important;
}

.btn-outline-light:hover .position-absolute {
  opacity: 1 !important;
}

/* Premium Card Hover Effects */
.main-premium-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 35px 70px rgba(0,0,0,0.5) !important;
}

.stat-item:hover {
  transform: scale(1.05);
  transition: all 0.3s ease;
}

/* Floating Service Cards Enhanced */
.floating-service-card:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 25px 50px rgba(0,0,0,0.3) !important;
}

/* Hero Badge Pulse Effect */
.hero-badge {
  animation: pulseGlow 3s ease-in-out infinite;
}

/* Title Word Animation */
.title-word {
  display: inline-block;
  animation: textGlow 4s ease-in-out infinite;
}

/* ===== SERVICE CARD EFFECTS ===== */

/* Service Card Hover Effects */
.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0,0,0,0.15) !important;
}

.service-card:hover .service-icon {
  transform: scale(1.1) rotate(5deg);
}

/* Feature Item Hover Effects */
.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.12) !important;
}

.feature-item:hover .feature-icon {
  transform: scale(1.1);
}

/* CTA Section Hover */
.cta-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
}

/* ===== RESPONSIVE HERO ADJUSTMENTS ===== */

@media (max-width: 768px) {
  .hero-section {
    min-height: 50vh !important;
    padding-top: 60px !important;
  }

  .hero-section .row {
    min-height: 35vh !important;
  }

  .floating-service-card {
    display: none !important;
  }

  .feature-card {
    margin-bottom: 8px;
  }

  .main-compact-card {
    margin-top: 20px;
    padding: 20px !important;
  }

  .hero-badge {
    font-size: 0.7rem !important;
    padding: 6px 12px !important;
  }

  .title-word {
    font-size: clamp(1.8rem, 8vw, 2.5rem) !important;
  }

  .hero-description {
    padding: 15px !important;
    margin-bottom: 15px !important;
  }

  .compact-icon {
    width: 50px !important;
    height: 50px !important;
  }

  .stat-number {
    font-size: 1.2rem !important;
  }
}

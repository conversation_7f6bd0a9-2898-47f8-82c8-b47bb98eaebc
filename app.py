from flask import Flask, render_template, Response
from datetime import datetime

app = Flask(__name__)

@app.route('/')
def index():
    return render_template("index.html")

@app.route('/hakkimizda')
def hakkimizda():
    return render_template("hakkimizda.html")

@app.route('/iletisim')
def iletisim():
    return render_template("iletisim.html")

@app.route('/hizmetler')
def hizmetler():
    return render_template("hizmetler.html")

@app.route('/hizmetler/klima')
def klima():
    return render_template('klima.html')

@app.route('/hizmetler/kombi')
def kombi():
    return render_template('kombi.html')

@app.route('/hizmetler/insaat')
def insaat():
    return render_template('insaat.html')

@app.route('/hizmetler/boya-badana')
def boya_badana():
    return render_template('boya_badana.html')

@app.route('/urunler')
def urunler():
    return render_template("urunler.html")

@app.route('/sss')
def sss():
    return render_template("sss.html")

@app.route('/sitemap.xml', methods=['GET'])
def sitemap():
    pages = []
    lastmod = datetime.now().date().isoformat()

    # Ana sayfalar
    pages.append(f"""<url>
        <loc>https://senkalgrup.com/</loc>
        <lastmod>{lastmod}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>1.0</priority>
    </url>""")

    # Genel sayfalar
    extra_pages = [
        ('/hakkimizda', 0.8),
        ('/iletisim', 0.8),
        ('/hizmetler', 0.9),
        ('/urunler', 0.9),
        ('/sss', 0.7)
    ]
    for path, priority in extra_pages:
        pages.append(f"""<url>
            <loc>https://senkalgrup.com{path}</loc>
            <lastmod>{lastmod}</lastmod>
            <changefreq>monthly</changefreq>
            <priority>{priority}</priority>
        </url>""")

    # Hizmetlerin detay sayfaları
    hizmet_detaylari = [
        '/hizmetler/klima',
        '/hizmetler/kombi',
        '/hizmetler/insaat',
        '/hizmetler/boya-badana'
    ]
    for path in hizmet_detaylari:
        pages.append(f"""<url>
            <loc>https://senkalgrup.com{path}</loc>
            <lastmod>{lastmod}</lastmod>
            <changefreq>monthly</changefreq>
            <priority>0.9</priority>
        </url>""")

    sitemap_xml = f"""<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        {''.join(pages)}
    </urlset>"""

    return Response(sitemap_xml, mimetype='application/xml')

@app.route('/robots.txt')
def robots():
    robots_txt = """User-agent: *
Allow: /

# Sitemap
Sitemap: /sitemap.xml

# Specific crawl directives
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 1

# Block unnecessary files
Disallow: /static/css/
Disallow: /static/js/
Disallow: /*.css$
Disallow: /*.js$

# Allow important static files
Allow: /static/img/
Allow: /static/images/"""

    return Response(robots_txt, mimetype='text/plain')

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8000, debug=True)

